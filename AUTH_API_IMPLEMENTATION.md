# Authentication API Implementation

This document describes the implementation of the authentication API for the PetaTalenta frontend application.

## API Base URL
```
https://api.chhrone.web.id
```

## Implemented Features

### 1. Authentication Service (`src/services/authService.js`)

A comprehensive service that handles all authentication-related API calls:

- **User Registration** - `POST /api/auth/register`
- **User Login** - `POST /api/auth/login`
- **User Logout** - `POST /api/auth/logout`
- **Change Password** - `POST /api/auth/change-password`
- **Delete Account** - `DELETE /api/auth/account`
- **Get Token Balance** - `GET /api/auth/token-balance`

### 2. Updated Auth Page (`src/pages/auth.js`)

Enhanced authentication page with:
- **Login Form** - Email and password validation
- **Register Form** - Email, password, and confirmation validation
- **Form Toggle** - Switch between login and register modes
- **Loading States** - Visual feedback during API calls
- **Error Handling** - Display API errors to users
- **Success Messages** - Confirmation of successful operations

### 3. Enhanced Dashboard (`src/pages/dashboard.js`)

Updated dashboard with:
- **Token Balance Display** - Shows current user token balance
- **Refresh Token Balance** - Manual refresh functionality
- **Proper Logout** - Uses auth service for logout

### 4. Enhanced Profile Page (`src/pages/profile.js`)

Updated profile page with:
- **Change Password Form** - Secure password change functionality
- **Delete Account** - Soft delete account functionality
- **User Information Display** - Shows current user data
- **Loading States** - Visual feedback for operations
- **Error Handling** - Proper error display

## API Validation

### Registration Validation
- **Email**: Valid email format, maximum 255 characters
- **Password**: Minimum 8 characters, must contain at least one letter and one number

### Login Validation
- **Email**: Valid email format
- **Password**: Required (no specific format validation for login)

### Change Password Validation
- **Current Password**: Required
- **New Password**: Minimum 8 characters, must contain at least one letter and one number

## Authentication Flow

1. **Registration/Login**: User provides credentials
2. **Token Storage**: JWT token stored in localStorage
3. **User Data Storage**: User information stored in localStorage
4. **Authentication Check**: Router checks token for protected routes
5. **API Calls**: All authenticated requests include Bearer token
6. **Logout**: Clears local storage and calls logout API

## Error Handling

The implementation includes comprehensive error handling:
- **Network Errors**: Graceful handling of connection issues
- **API Errors**: Display server error messages to users
- **Validation Errors**: Client-side validation with user feedback
- **Token Expiry**: Automatic redirect to login when token is invalid

## Security Features

- **JWT Token Authentication**: Secure token-based authentication
- **Password Validation**: Strong password requirements
- **Soft Delete**: Account deletion preserves data integrity
- **Rate Limiting**: API includes rate limiting (handled server-side)

## Testing

### Development Testing Utility (`src/utils/authTest.js`)

A comprehensive testing utility for development:

```javascript
// Test individual functions
testAuth.register('<EMAIL>', 'password123')
testAuth.login('<EMAIL>', 'password123')
testAuth.tokenBalance()
testAuth.changePassword('oldPass', 'newPass')
testAuth.logout()
testAuth.deleteAccount()

// Run full test suite
testAuth.fullTest()

// Check authentication status
testAuth.status()

// Clear authentication data
testAuth.clear()
```

### Manual Testing

1. **Open browser console** in development
2. **Use test utilities** to verify API integration
3. **Test UI flows** through the application interface
4. **Verify error handling** with invalid credentials

## File Structure

```
src/
├── services/
│   └── authService.js          # Main authentication service
├── pages/
│   ├── auth.js                 # Login/Register page
│   ├── dashboard.js            # Dashboard with token balance
│   └── profile.js              # Profile with password/account management
├── utils/
│   └── authTest.js             # Development testing utility
└── router.js                   # Updated with auth integration
```

## Usage Examples

### Register a New User
```javascript
import { authService } from './services/authService.js';

const result = await authService.register('<EMAIL>', 'myPassword123');
if (result.success) {
  console.log('User registered:', result.data.user);
  console.log('Token:', result.data.token);
}
```

### Login User
```javascript
const result = await authService.login('<EMAIL>', 'myPassword123');
if (result.success) {
  console.log('Login successful:', result.data.user);
}
```

### Get Token Balance
```javascript
const result = await authService.getTokenBalance();
if (result.success) {
  console.log('Token balance:', result.data.tokenBalance);
}
```

### Change Password
```javascript
const result = await authService.changePassword('currentPass', 'newPass123');
if (result.success) {
  console.log('Password changed successfully');
}
```

### Delete Account
```javascript
const result = await authService.deleteAccount();
if (result.success) {
  console.log('Account deleted:', result.data);
}
```

## API Response Examples

### Successful Registration
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "email": "<EMAIL>",
      "username": null,
      "user_type": "user",
      "is_active": true,
      "token_balance": 5,
      "created_at": "2024-01-15T10:30:00.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "User registered successfully"
}
```

### Token Balance Response
```json
{
  "success": true,
  "data": {
    "userId": "550e8400-e29b-41d4-a716-************",
    "tokenBalance": 5,
    "lastUpdated": "2024-01-15T10:30:00.000Z"
  }
}
```

## Next Steps

1. **Test the implementation** using the provided testing utilities
2. **Customize UI/UX** as needed for your application
3. **Add additional validation** if required
4. **Implement password reset** functionality if needed
5. **Add user profile management** features as required

## Notes

- All API calls include proper error handling and fallbacks
- The implementation follows modern JavaScript practices
- Authentication state is managed through localStorage
- The router automatically handles authentication checks
- All forms include loading states and user feedback
